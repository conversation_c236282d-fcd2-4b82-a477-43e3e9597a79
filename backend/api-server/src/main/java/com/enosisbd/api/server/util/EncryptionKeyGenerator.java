package com.enosisbd.api.server.util;

import com.enosisbd.api.server.service.security.TokenEncryptionService;

/**
 * Utility class to generate encryption keys for OAuth token security
 * 
 * Usage:
 * 1. Run this class to generate a new encryption key
 * 2. Set the generated key as environment variable: app.security.token.encryption.key
 * 3. Use different keys for different environments (dev/staging/prod)
 */
public class EncryptionKeyGenerator {
    
    public static void main(String[] args) {
        System.out.println("=== OAuth Token Encryption Key Generator ===");
        System.out.println();
        
        // Generate a new AES-256 encryption key
        String encryptionKey = TokenEncryptionService.generateEncryptionKey();
        
        System.out.println("Generated AES-256 Encryption Key:");
        System.out.println(encryptionKey);
        System.out.println();
        
        System.out.println("Environment Variable Setup:");
        System.out.println("export app.security.token.encryption.key=" + encryptionKey);
        System.out.println();
        
        System.out.println("Docker Environment:");
        System.out.println("docker run -e app.security.token.encryption.key=" + encryptionKey + " your-app");
        System.out.println();
        
        System.out.println("application.yaml:");
        System.out.println("app:");
        System.out.println("  security:");
        System.out.println("    token:");
        System.out.println("      encryption:");
        System.out.println("        key: " + encryptionKey);
        System.out.println();
        
        System.out.println("IMPORTANT SECURITY NOTES:");
        System.out.println("1. Keep this key secure and never commit it to version control");
        System.out.println("2. Use different keys for different environments");
        System.out.println("3. Store the key in secure environment variables or secret management");
        System.out.println("4. Implement key rotation strategy for production");
        System.out.println("5. Backup the key securely - losing it means losing all stored tokens");
    }
}
