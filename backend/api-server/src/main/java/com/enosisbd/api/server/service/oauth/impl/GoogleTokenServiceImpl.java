package com.enosisbd.api.server.service.oauth.impl;

import com.enosisbd.api.server.entity.User;
import com.enosisbd.api.server.service.oauth.GoogleTokenService;
import com.enosisbd.api.server.service.security.TokenEncryptionService;
import com.enosisbd.api.server.service.user.UserService;
import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Log4j2
public class GoogleTokenServiceImpl implements GoogleTokenService {

    private final UserService userService;
    private final RestTemplate restTemplate;
    private final TokenEncryptionService tokenEncryptionService;

    @Value("${spring.security.oauth2.client.registration.google.client-id}")
    private String googleClientId;

    @Value("${spring.security.oauth2.client.registration.google.client-secret}")
    private String googleClientSecret;

    private static final List<String> SCOPES = Collections.singletonList(
            "https://www.googleapis.com/auth/spreadsheets.readonly"
    );

    @Override
    public void storeTokens(User user, String accessToken, String refreshToken, Integer expiresIn) {
        log.info("Storing encrypted Google OAuth tokens for user: {}", user.getEmail());

        try {
            // Encrypt tokens before storing
            String encryptedAccessToken = tokenEncryptionService.encryptToken(accessToken);
            String encryptedRefreshToken = tokenEncryptionService.encryptToken(refreshToken);

            user.setGoogleAccessToken(encryptedAccessToken);
            user.setGoogleRefreshToken(encryptedRefreshToken);

            if (expiresIn != null) {
                user.setGoogleTokenExpiry(LocalDateTime.now().plusSeconds(expiresIn));
            } else {
                // Default to 1 hour if no expiry provided
                user.setGoogleTokenExpiry(LocalDateTime.now().plusHours(1));
            }

            userService.persist(user);
            log.info("Successfully stored encrypted tokens for user: {}", user.getEmail());

        } catch (Exception e) {
            log.error("Failed to store encrypted tokens for user: {}", user.getEmail(), e);
            throw new RuntimeException("Failed to store OAuth tokens securely", e);
        }
    }

    @Override
    public GoogleCredentials getValidCredentials(String userEmail) throws IOException {
        Optional<User> userOpt = userService.findByEmail(userEmail);
        if (userOpt.isEmpty()) {
            log.warn("User not found: {}", userEmail);
            return null;
        }

        User user = userOpt.get();

        // Check if user has tokens
        if (user.getGoogleAccessToken() == null) {
            log.warn("No Google access token found for user: {}", userEmail);
            return null;
        }

        // Check if token is expired and refresh if needed
        if (isTokenExpired(user)) {
            log.info("Token expired for user: {}, attempting refresh", userEmail);
            if (!refreshTokens(user)) {
                log.error("Failed to refresh tokens for user: {}", userEmail);
                return null;
            }
            // Reload user after refresh
            user = userService.findByEmail(userEmail).orElse(null);
            if (user == null || user.getGoogleAccessToken() == null) {
                return null;
            }
        }

        try {
            // Decrypt the access token
            String decryptedAccessToken = tokenEncryptionService.decryptToken(user.getGoogleAccessToken());

            // Create GoogleCredentials with the decrypted access token
            AccessToken accessToken = new AccessToken(
                    decryptedAccessToken,
                    java.util.Date.from(user.getGoogleTokenExpiry().atZone(java.time.ZoneId.systemDefault()).toInstant())
            );

            return GoogleCredentials.create(accessToken).createScoped(SCOPES);

        } catch (Exception e) {
            log.error("Failed to decrypt access token for user: {}", userEmail, e);
            return null;
        }
    }

    @Override
    public boolean hasValidTokens(String userEmail) {
        Optional<User> userOpt = userService.findByEmail(userEmail);
        if (userOpt.isEmpty()) {
            return false;
        }

        User user = userOpt.get();
        return user.getGoogleAccessToken() != null && 
               (user.getGoogleRefreshToken() != null || !isTokenExpired(user));
    }

    @Override
    public boolean refreshTokens(User user) throws IOException {
        if (user.getGoogleRefreshToken() == null) {
            log.warn("No refresh token available for user: {}", user.getEmail());
            return false;
        }

        try {
            log.info("Refreshing Google tokens for user: {}", user.getEmail());

            // Decrypt the refresh token
            String decryptedRefreshToken = tokenEncryptionService.decryptToken(user.getGoogleRefreshToken());

            String tokenUrl = "https://oauth2.googleapis.com/token";
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            MultiValueMap<String, String> tokenRequest = new LinkedMultiValueMap<>();
            tokenRequest.add("client_id", googleClientId);
            tokenRequest.add("client_secret", googleClientSecret);
            tokenRequest.add("refresh_token", decryptedRefreshToken);
            tokenRequest.add("grant_type", "refresh_token");

            HttpEntity<MultiValueMap<String, String>> tokenEntity = new HttpEntity<>(tokenRequest, headers);
            ResponseEntity<Map> response = restTemplate.exchange(tokenUrl, HttpMethod.POST, tokenEntity, Map.class);

            if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
                Map<String, Object> tokenData = response.getBody();
                String newAccessToken = (String) tokenData.get("access_token");
                Integer expiresIn = (Integer) tokenData.get("expires_in");

                if (newAccessToken != null) {
                    // Encrypt the new access token before storing
                    String encryptedAccessToken = tokenEncryptionService.encryptToken(newAccessToken);
                    user.setGoogleAccessToken(encryptedAccessToken);

                    if (expiresIn != null) {
                        user.setGoogleTokenExpiry(LocalDateTime.now().plusSeconds(expiresIn));
                    }

                    // Update refresh token if provided
                    String newRefreshToken = (String) tokenData.get("refresh_token");
                    if (newRefreshToken != null) {
                        String encryptedRefreshToken = tokenEncryptionService.encryptToken(newRefreshToken);
                        user.setGoogleRefreshToken(encryptedRefreshToken);
                    }

                    userService.persist(user);
                    log.info("Successfully refreshed and encrypted tokens for user: {}", user.getEmail());
                    return true;
                }
            }

            log.error("Failed to refresh tokens for user: {}, response: {}", user.getEmail(), response.getStatusCode());
            return false;

        } catch (Exception e) {
            log.error("Error refreshing tokens for user: {}", user.getEmail(), e);
            return false;
        }
    }

    @Override
    public void clearTokens(String userEmail) {
        Optional<User> userOpt = userService.findByEmail(userEmail);
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            user.setGoogleAccessToken(null);
            user.setGoogleRefreshToken(null);
            user.setGoogleTokenExpiry(null);
            userService.persist(user);
            log.info("Cleared Google tokens for user: {}", userEmail);
        }
    }

    private boolean isTokenExpired(User user) {
        if (user.getGoogleTokenExpiry() == null) {
            return true;
        }
        // Add 5 minute buffer to avoid edge cases
        return LocalDateTime.now().plusMinutes(5).isAfter(user.getGoogleTokenExpiry());
    }
}
