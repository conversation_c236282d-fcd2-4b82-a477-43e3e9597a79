package com.enosisbd.api.server.service.oauth;

import com.enosisbd.api.server.entity.User;
import com.google.auth.oauth2.GoogleCredentials;

import java.io.IOException;
import java.time.LocalDateTime;

/**
 * Service for managing Google OAuth tokens for users
 */
public interface GoogleTokenService {
    
    /**
     * Store Google OAuth tokens for a user
     * 
     * @param user The user to store tokens for
     * @param accessToken The Google access token
     * @param refreshToken The Google refresh token (optional)
     * @param expiresIn Token expiration time in seconds
     */
    void storeTokens(User user, String accessToken, String refreshToken, Integer expiresIn);
    
    /**
     * Get valid Google credentials for a user
     * Will refresh the token if it's expired
     * 
     * @param userEmail The user's email
     * @return GoogleCredentials object or null if no valid tokens
     * @throws IOException if there's an error refreshing tokens
     */
    GoogleCredentials getValidCredentials(String userEmail) throws IOException;
    
    /**
     * Check if a user has valid Google tokens
     * 
     * @param userEmail The user's email
     * @return true if user has valid tokens
     */
    boolean hasValidTokens(String userEmail);
    
    /**
     * Refresh expired tokens for a user
     * 
     * @param user The user whose tokens need refreshing
     * @return true if refresh was successful
     * @throws IOException if there's an error during refresh
     */
    boolean refreshTokens(User user) throws IOException;
    
    /**
     * Clear stored tokens for a user
     * 
     * @param userEmail The user's email
     */
    void clearTokens(String userEmail);
}
