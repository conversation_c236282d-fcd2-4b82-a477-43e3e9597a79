package com.enosisbd.api.server.service.googlesheets.impl;

import com.enosisbd.api.server.config.GoogleSheetsProperties;
import com.enosisbd.api.server.dto.SheetTestCaseDto;
import com.enosisbd.api.server.dto.SubModuleRangeDto;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.exception.ForbiddenRestException;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.oauth.GoogleTokenService;
import com.enosisbd.api.server.service.user.UserService;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.http.HttpRequestInitializer;
import com.google.api.client.http.javanet.NetHttpTransport;
import com.google.api.client.json.JsonFactory;
import com.google.api.client.json.gson.GsonFactory;
import com.google.api.services.sheets.v4.Sheets;
import com.google.api.services.sheets.v4.SheetsScopes;
import com.google.api.services.sheets.v4.model.Sheet;
import com.google.api.services.sheets.v4.model.Spreadsheet;
import com.google.api.services.sheets.v4.model.ValueRange;
import com.google.auth.http.HttpCredentialsAdapter;
import com.google.auth.oauth2.GoogleCredentials;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Log4j2
public class GoogleSheetsServiceImpl implements GoogleSheetsService {

    private static final JsonFactory JSON_FACTORY = GsonFactory.getDefaultInstance();
    private static final List<String> SCOPES = Collections.singletonList(
            SheetsScopes.SPREADSHEETS_READONLY
    );
    private static final String APPLICATION_NAME = "Enosis Portal Google Sheets Integration";

    private final ResourceLoader resourceLoader;
    private final AuthorizationService authorizationService;
    private final UserService userService;
    private final GoogleSheetsProperties googleSheetsProperties;
    private final GoogleTokenService googleTokenService;

    @Autowired(required = false)
    private ClientRegistrationRepository clientRegistrationRepository;

    @Override
    public String extractSheetId(String sheetUrl) {
        // Extract the sheet ID from the URL
        // Example URL: https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit
        Pattern pattern = Pattern.compile("/spreadsheets/d/([a-zA-Z0-9-_]+)");
        Matcher matcher = pattern.matcher(sheetUrl);

        if (matcher.find()) {
            return matcher.group(1);
        }

        throw new BadRequestRestException("Invalid Google Sheets URL format");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<String> getSheetNames(String sheetId, String userEmail) throws IOException, GeneralSecurityException {
        // Check if the user has access to the sheet
        if (userEmail != null && !hasAccessToSheet(sheetId, userEmail)) {
            throw new ForbiddenRestException("You don't have access to this Google Sheet");
        }

        Sheets service = getSheetService(userEmail);
        Spreadsheet spreadsheet = service.spreadsheets().get(sheetId).execute();

        return spreadsheet.getSheets().stream()
                .map(Sheet::getProperties)
                .map(properties -> properties.getTitle())
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<List<Object>> getSheetData(String sheetId, String sheetName, String userEmail) throws IOException, GeneralSecurityException {
        // Check if the user has access to the sheet
        if (userEmail != null && !hasAccessToSheet(sheetId, userEmail)) {
            throw new ForbiddenRestException("You don't have access to this Google Sheet");
        }

        Sheets service = getSheetService(userEmail);
        String range = sheetName;

        ValueRange response = service.spreadsheets().values()
                .get(sheetId, range)
                .execute();

        return response.getValues();
    }

    @Override
    public boolean hasAccessToSheet(String sheetId, String userEmail) throws IOException, GeneralSecurityException {
        if (userEmail == null) {
            // If no user email is provided, we'll use the service account which can only access public sheets
            log.warn("No user email provided for access check, using service account");
            return true;
        }

        try {
            // Try to access the sheet's metadata to check permissions
            // If we can get the spreadsheet info, the user has access
            log.info("Checking if user {} has access to sheet {}", userEmail, sheetId);
            Sheets service = getSheetService(userEmail);
            service.spreadsheets().get(sheetId)
                    .setFields("spreadsheetId") // Minimal fields to reduce response size
                    .execute();
            log.info("User {} has access to sheet {}", userEmail, sheetId);
            return true;
        } catch (Exception e) {
            log.error("Error checking access to sheet for user {}: {}", userEmail, e.getMessage());
            return false;
        }
    }

    @Override
    public List<SubModuleRangeDto> extractSubmodules(List<List<Object>> sheetData, int columnIndex, int startRow) {
        List<SubModuleRangeDto> submodules = new ArrayList<>();

        if (sheetData == null || sheetData.isEmpty() || startRow >= sheetData.size()) {
            return submodules;
        }

        String currentSubmoduleName = null;
        int currentStartRow = -1;

        // Process rows starting from the specified start row
        for (int rowIndex = startRow; rowIndex < sheetData.size(); rowIndex++) {
            List<Object> row = sheetData.get(rowIndex);

            // Skip rows that don't have enough columns
            if (row.size() <= columnIndex) {
                continue;
            }

            Object cellValue = row.get(columnIndex);
            String cellValueStr = cellValue != null ? cellValue.toString().trim() : "";

            // If we find a non-empty cell in the specified column
            if (!cellValueStr.isEmpty()) {
                // If we already have a current submodule, add it to the list
                if (currentSubmoduleName != null) {
                    int currentEndRow = rowIndex - 1;
                    String range = (currentStartRow + 1) + "-" + (currentEndRow + 1); // Convert to 1-based indices for display

                    submodules.add(SubModuleRangeDto.builder()
                            .name(currentSubmoduleName)
                            .range(range)
                            .startRow(currentStartRow)
                            .endRow(currentEndRow)
                            .build());
                }

                // Start a new submodule
                currentSubmoduleName = cellValueStr;
                currentStartRow = rowIndex;
            }
        }

        // Add the last submodule if there is one
        if (currentSubmoduleName != null) {
            int currentEndRow = sheetData.size() - 1;
            String range = (currentStartRow + 1) + "-" + (currentEndRow + 1); // Convert to 1-based indices for display

            submodules.add(SubModuleRangeDto.builder()
                    .name(currentSubmoduleName)
                    .range(range)
                    .startRow(currentStartRow)
                    .endRow(currentEndRow)
                    .build());
        }

        return submodules;
    }

    @Override
    public int columnLetterToIndex(String column) {
        if (column == null || column.isEmpty()) {
            throw new BadRequestRestException("Column letter cannot be null or empty");
        }

        column = column.toUpperCase();
        if (!column.matches("^[A-Z]$")) {
            throw new BadRequestRestException("Column must be a single letter (A-Z)");
        }

        // Convert column letter to 0-based index (A=0, B=1, etc.)
        return column.charAt(0) - 'A';
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    public List<SheetTestCaseDto> getTestCases(
            String sheetId,
            String sheetName,
            int startRow,
            int endRow,
            String caseIdColumn,
            String descriptionColumn,
            String expectedResultColumn,
            String userEmail) throws IOException, GeneralSecurityException {

        // Check if the user has access to the sheet
        if (userEmail != null && !hasAccessToSheet(sheetId, userEmail)) {
            throw new ForbiddenRestException("You don't have access to this Google Sheet");
        }

        // Get sheet data
        List<List<Object>> sheetData = getSheetData(sheetId, sheetName, userEmail);

        // Convert column letters to indices
        int caseIdColumnIndex = columnLetterToIndex(caseIdColumn);
        int descriptionColumnIndex = columnLetterToIndex(descriptionColumn);
        int expectedResultColumnIndex = columnLetterToIndex(expectedResultColumn);

        // Validate row range
        if (startRow < 0) {
            startRow = 0;
        }

        if (endRow >= sheetData.size()) {
            endRow = sheetData.size() - 1;
        }

        if (startRow > endRow) {
            throw new BadRequestRestException("Invalid row range: start row cannot be greater than end row");
        }

        // Extract test cases from the specified range
        List<SheetTestCaseDto> testCases = new ArrayList<>();

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++) {
            List<Object> row = sheetData.get(rowIndex);

            // Skip rows that don't have enough columns
            int maxColumnIndex = Math.max(Math.max(caseIdColumnIndex, descriptionColumnIndex), expectedResultColumnIndex);
            if (row.size() <= maxColumnIndex) {
                continue;
            }

            // Extract values from the row
            String caseId = getCellValueAsString(row, caseIdColumnIndex);
            String description = getCellValueAsString(row, descriptionColumnIndex);
            String expectedResult = getCellValueAsString(row, expectedResultColumnIndex);

            // Create test case DTO
            SheetTestCaseDto testCase = SheetTestCaseDto.builder()
                    .caseId(caseId)
                    .description(description)
                    .expectedResult(expectedResult)
                    .build();

            testCases.add(testCase);
        }

        return testCases;
    }

    /**
     * Get cell value as string, handling null values
     *
     * @param row The row data
     * @param columnIndex The column index
     * @return The cell value as string, or empty string if null
     */
    private String getCellValueAsString(List<Object> row, int columnIndex) {
        if (columnIndex >= row.size()) {
            return "";
        }

        Object cellValue = row.get(columnIndex);
        return cellValue != null ? cellValue.toString().trim() : "";
    }

    private Sheets getSheetService(String userEmail) throws IOException, GeneralSecurityException {
        NetHttpTransport httpTransport = GoogleNetHttpTransport.newTrustedTransport();
        HttpRequestInitializer requestInitializer;

        // If we're using service account authentication or no user email is provided
        if (googleSheetsProperties.isUseServiceAccount() || userEmail == null) {
            // Use service account credentials
            log.info("Using service account credentials for Google Sheets access");
            GoogleCredentials credentials = getServiceAccountCredentials();
            requestInitializer = new HttpCredentialsAdapter(credentials);
        } else {
            // Try to use user-specific OAuth credentials first
            log.info("Attempting to use user-specific OAuth credentials for Google Sheets access for user: {}", userEmail);

            GoogleCredentials userCredentials = googleTokenService.getValidCredentials(userEmail);
            if (userCredentials != null) {
                log.info("Successfully obtained user OAuth credentials for user: {}", userEmail);
                requestInitializer = new HttpCredentialsAdapter(userCredentials);
            } else {
                // Fallback to service account if user doesn't have valid tokens
                log.warn("No valid OAuth tokens found for user: {}, falling back to service account", userEmail);
                GoogleCredentials credentials = getServiceAccountCredentials();
                requestInitializer = new HttpCredentialsAdapter(credentials);
            }
        }

        return new Sheets.Builder(httpTransport, JSON_FACTORY, requestInitializer)
                .setApplicationName(APPLICATION_NAME)
                .build();
    }

    /**
     * Get Google service account credentials for accessing Google Sheets API
     *
     * @return GoogleCredentials object with appropriate scopes
     * @throws IOException if there's an error reading the credentials file
     */
    private GoogleCredentials getServiceAccountCredentials() throws IOException {
        log.info("Loading service account credentials from file");
        Resource resource = resourceLoader.getResource(googleSheetsProperties.getCredentialsFile());
        try (InputStream in = resource.getInputStream()) {
            return GoogleCredentials.fromStream(in).createScoped(SCOPES);
        } catch (IOException e) {
            log.error("Error loading Google service account credentials: {}", e.getMessage());
            throw new BadRequestRestException("Unable to load Google service account credentials: " + e.getMessage());
        }
    }
}
