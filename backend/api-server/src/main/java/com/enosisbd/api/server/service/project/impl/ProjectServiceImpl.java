package com.enosisbd.api.server.service.project.impl;

import com.enosisbd.api.server.config.GoogleSheetsProperties;
import com.enosisbd.api.server.dto.ModuleDto;
import com.enosisbd.api.server.dto.ProjectDto;
import com.enosisbd.api.server.dto.SubModuleDto;
import com.enosisbd.api.server.dto.SubModuleRangeDto;
import com.enosisbd.api.server.entity.Project;
import com.enosisbd.api.server.entity.SubModule;
import com.enosisbd.api.server.exception.BadRequestRestException;
import com.enosisbd.api.server.model.EntityType;
import com.enosisbd.api.server.repository.ProjectRepository;
import com.enosisbd.api.server.service.authorization.AuthorizationService;
import com.enosisbd.api.server.service.entitySharing.EntitySharingService;
import com.enosisbd.api.server.service.googlesheets.GoogleSheetsService;
import com.enosisbd.api.server.service.module.ModuleService;
import com.enosisbd.api.server.service.project.ProjectService;
import com.enosisbd.api.server.service.submodule.SubModuleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.log4j.Log4j2;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * The type Project service.
 */
@Service
@RequiredArgsConstructor
@Log4j2
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository repository;
    private final EntitySharingService entitySharingService;
    private final AuthorizationService authorizationService;
    private final GoogleSheetsService googleSheetsService;
    private final ModuleService moduleService;
    private final SubModuleService subModuleService;
    private final GoogleSheetsProperties googleSheetsProperties;

    /**
     * Find all Project list.
     *
     * @return the project list
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public List<ProjectDto> findAll() {
        List<Project> accessibleProjects =
                authorizationService.isCurrentUserAdmin()
                        ? repository.findAllByOrderByUpdatedAtDesc()
                        : repository.findBySharedWithUserOrderByUpdatedAtDesc(
                        authorizationService.getCurrentUsername(),
                        authorizationService.getCurrentUserId());

        // Convert to DTOs
        return accessibleProjects.stream()
                .map(this::convertToDto)
                .toList();
    }

    /**
     * Exists by project id boolean.
     *
     * @param id the id
     * @return the boolean
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public boolean existsById(Long id) {
        Optional<Project> project = repository.findById(id);
        if (project.isEmpty()) {
            return false;
        }

        return entitySharingService.hasAccess(project.get(), EntityType.PROJECT);
    }

    /**
     * Gets project by id.
     *
     * @param id the id
     * @return the by id
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ProjectDto> getById(Long id) {
        Optional<Project> project = repository.findById(id);

        if (project.isEmpty()) {
            return Optional.empty();
        }

        return project.map(this::convertToDto);
    }

    /**
     * Add project to the database.
     * If a Google Sheet URL is provided, it will process the Google Sheet first,
     * extract the Google Sheet ID, and then save the project with the Google Sheet ID.
     *
     * @param dto the dto
     * @return the project dto
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public ProjectDto add(ProjectDto dto) {
        // Check if Google Sheet URL is provided
        if (StringUtils.hasText(dto.getGoogleSheetUrl())) {
            try {
                // Extract sheet ID from URL before saving
                String sheetId = googleSheetsService.extractSheetId(dto.getGoogleSheetUrl());
                dto.setGoogleSheetId(sheetId);

                // Get the current user's email for Google OAuth access
                String userEmail = authorizationService.getCurrentUsername();
                log.info("Using current user email for Google Sheets access check: {}", userEmail);

                // Check if the user has access to the sheet before proceeding (if checkAccess is enabled)
                if (googleSheetsProperties.isCheckAccess() && !googleSheetsService.hasAccessToSheet(sheetId, userEmail)) {
                    throw new BadRequestRestException("You don't have access to this Google Sheet. Please make sure the sheet is shared with your Google account.");
                }

                // Set the user email for Google OAuth access
                dto.setGoogleUserEmail(userEmail);
            } catch (Exception e) {
                log.error("Error checking access to Google Sheet: {}", e.getMessage(), e);
                throw new BadRequestRestException("Error checking access to Google Sheet: " + e.getMessage());
            }
        }

        // Save the project with the Google Sheet ID if available
        Project entity = convertToEntity(dto);
        repository.save(entity);
        ProjectDto savedDto = convertToDto(entity);

        // Process Google Sheet data if URL is provided
        if (StringUtils.hasText(dto.getGoogleSheetUrl())) {
            try {
                // Process Google Sheet and update the project with modules and submodules
                processGoogleSheet(savedDto);

                // Refresh the DTO with the updated entity data
                Optional<Project> updatedProject = repository.findById(entity.getId());
                if (updatedProject.isPresent()) {
                    savedDto = convertToDto(updatedProject.get());
                }
            } catch (Exception e) {
                log.error("Error processing Google Sheet: {}", e.getMessage(), e);
                throw new BadRequestRestException("Error processing Google Sheet: " + e.getMessage());
            }
        }

        return savedDto;
    }

    /**
     * Process Google Sheet data to create modules and submodules
     *
     * @param projectDto The project DTO with Google Sheet information
     * @throws IOException If there is an error accessing the Google Sheet
     * @throws GeneralSecurityException If there is a security error
     */
    private void processGoogleSheet(ProjectDto projectDto) throws IOException, GeneralSecurityException {
        log.info("Processing Google Sheet for project {}: {}", projectDto.getId(), projectDto.getGoogleSheetUrl());

        // Use the sheet ID and user email that were already set during project creation
        String sheetId = projectDto.getGoogleSheetId();
        String userEmail = projectDto.getGoogleUserEmail();

        if (sheetId == null) {
            // If somehow the sheet ID is not set, extract it from the URL
            sheetId = googleSheetsService.extractSheetId(projectDto.getGoogleSheetUrl());
            projectDto.setGoogleSheetId(sheetId);
        }

        if (userEmail == null) {
            // If somehow the user email is not set, get it from the current user
            userEmail = authorizationService.getCurrentUsername();
            projectDto.setGoogleUserEmail(userEmail);
        }

        log.info("Using Google Sheet ID: {} and user email: {}", sheetId, userEmail);

        // Get sheet names
        List<String> sheetNames = googleSheetsService.getSheetNames(sheetId, userEmail);
        if (sheetNames.isEmpty()) {
            throw new BadRequestRestException("No sheets found in the Google Sheet");
        }

        // Filter out excluded tabs
        sheetNames = filterExcludedTabs(sheetNames, projectDto.getExcludedTabs());

        // Convert column letter to index (0-based)
        int columnIndex = googleSheetsService.columnLetterToIndex(projectDto.getSubmoduleColumn());

        // Convert start row to 0-based index (subtract 1)
        int startRowIndex = projectDto.getSubmoduleStartRow() - 1;
        if (startRowIndex < 0) {
            startRowIndex = 0;
        }

        // Process each sheet as a module
        for (String sheetName : sheetNames) {
            // Create module
            ModuleDto moduleDto = new ModuleDto();
            moduleDto.setName(sheetName);
            moduleDto.setProjectId(projectDto.getId());

            moduleDto = moduleService.add(moduleDto);

            // Get sheet data
            List<List<Object>> sheetData = googleSheetsService.getSheetData(sheetId, sheetName, userEmail);

            // Extract submodules
            List<SubModuleRangeDto> submoduleRanges = googleSheetsService.extractSubmodules(
                    sheetData, columnIndex, startRowIndex);

            // Create submodules
            for (SubModuleRangeDto submoduleRange : submoduleRanges) {
                SubModuleDto subModuleDto = new SubModuleDto();
                subModuleDto.setName(submoduleRange.getName());
                subModuleDto.setModuleId(moduleDto.getId());
                subModuleDto.setStartRow(submoduleRange.getStartRow());
                subModuleDto.setEndRow(submoduleRange.getEndRow());

                subModuleService.add(subModuleDto);
            }
        }

        // Make sure the project entity has the Google Sheet ID and user email
        Optional<Project> projectOpt = repository.findById(projectDto.getId());
        if (projectOpt.isPresent()) {
            Project project = projectOpt.get();
            if (!Objects.equals(project.getGoogleSheetId(), sheetId) ||
                !Objects.equals(project.getGoogleUserEmail(), userEmail)) {

                log.info("Updating project with Google Sheet ID and user email");
                project.setGoogleSheetId(sheetId);
                project.setGoogleUserEmail(userEmail);
                repository.save(project);

                // Update the DTO with the saved entity data
                convertToDto(projectDto, project);
            }
        }

        log.info("Finished processing Google Sheet for project {}", projectDto.getId());
    }

    /**
     * Update optional.
     *
     * @param dto the dto
     * @return the optional
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ProjectDto> update(ProjectDto dto) {
        var maybe = repository.findById(dto.getId());
        if (maybe.isEmpty()) return Optional.empty();

        Project existing = maybe.get();
        // Check if Google Sheet related fields have changed
        boolean googleSheetFieldsChanged =
            !Objects.equals(existing.getGoogleSheetUrl(), dto.getGoogleSheetUrl()) ||
            !Objects.equals(existing.getSubmoduleColumn(), dto.getSubmoduleColumn()) ||
            !Objects.equals(existing.getSubmoduleStartRow(), dto.getSubmoduleStartRow()) ||
            !Objects.equals(existing.getExcludedTabs(), dto.getExcludedTabs());

        // Update the project

        Project entity = convertToEntity(existing, dto);
        repository.save(entity);

        // If Google Sheet fields changed and URL is provided, refresh from Google Sheet
        if (googleSheetFieldsChanged && StringUtils.hasText(dto.getGoogleSheetUrl())) {
            try {
                log.info("Google Sheet fields changed, refreshing data from Google Sheet");
                refreshFromGoogleSheet(entity.getId());

                // Refresh the entity from the database
                maybe = repository.findById(dto.getId());
                if (maybe.isPresent()) {
                    entity = maybe.get();
                }
            } catch (Exception e) {
                log.error("Error refreshing Google Sheet data after update: {}", e.getMessage(), e);
                throw new BadRequestRestException("Error refreshing Google Sheet data: " + e.getMessage());
            }
        }
        return Optional.of(convertToDto(entity));
    }

    /**
     * Delete optional.
     *
     * @param id the id
     * @return the optional
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<Boolean> delete(Long id) {
        Optional<Project> project = repository.findById(id);
        if (project.isEmpty()) {
            return Optional.empty();
        }

        // Delete the project
        repository.delete(project.get());
        return Optional.of(true);
    }


    private ProjectDto convertToDto(Project entity) {
        return convertToDto(new ProjectDto(), entity);
    }

    /**
     * Refresh project data from Google Sheet
     *
     * @param id the project id
     * @return the updated project DTO
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED, isolation = Isolation.SERIALIZABLE)
    @Override
    public Optional<ProjectDto> refreshFromGoogleSheet(Long id) {
        // Find the project
        Optional<Project> projectOpt = repository.findById(id);
        if (projectOpt.isEmpty()) {
            return Optional.empty();
        }

        Project project = projectOpt.get();

        // Check if the project has Google Sheet URL
        if (!StringUtils.hasText(project.getGoogleSheetUrl())) {
            throw new BadRequestRestException("Project does not have a Google Sheet URL");
        }

        // Convert to DTO
        ProjectDto projectDto = convertToDto(project);

        try {
            // Delete existing modules and submodules
            moduleService.deleteByProjectId(id);

            // Process Google Sheet and create new modules and submodules
            processGoogleSheet(projectDto);

            // Refresh the project data
            projectOpt = repository.findById(id);
            if (projectOpt.isPresent()) {
                return Optional.of(convertToDto(projectOpt.get()));
            }

            return Optional.of(projectDto);
        } catch (Exception e) {
            log.error("Error refreshing Google Sheet data: {}", e.getMessage(), e);
            throw new BadRequestRestException("Error refreshing Google Sheet data: " + e.getMessage());
        }
    }

    private ProjectDto convertToDto(ProjectDto dto, Project entity) {
        dto.setId(entity.getId());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setUpdatedAt(entity.getUpdatedAt());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setCreatedBy(entity.getCreatedBy());

        // Set Google Sheet metadata
        dto.setGoogleSheetId(entity.getGoogleSheetId());
        dto.setGoogleSheetUrl(entity.getGoogleSheetUrl());
        dto.setSubmoduleColumn(entity.getSubmoduleColumn());
        dto.setSubmoduleStartRow(entity.getSubmoduleStartRow());
        dto.setGoogleUserEmail(entity.getGoogleUserEmail());
        dto.setCaseIdColumn(entity.getCaseIdColumn());
        dto.setTestCaseDescriptionColumn(entity.getTestCaseDescriptionColumn());
        dto.setTestCaseExpectedResultColumn(entity.getTestCaseExpectedResultColumn());
        dto.setExcludedTabs(entity.getExcludedTabs());

        // Check if the current user has direct (non-inherited) access to this project
        Long currentUserId = authorizationService.getCurrentUserId();
        boolean isDirectlyShared = entitySharingService.hasNonInheritedDirectAccess(
                EntityType.PROJECT, entity.getId(), currentUserId);

        return dto;
    }

    private Project convertToEntity(ProjectDto dto) {
        return convertToEntity(new Project(), dto);
    }

    private Project convertToEntity(Project entity, ProjectDto dto) {
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());

        // Set Google Sheet metadata if available
        if (dto.getGoogleSheetUrl() != null) {
            entity.setGoogleSheetUrl(dto.getGoogleSheetUrl());
            if (dto.getGoogleSheetId() != null) {
                entity.setGoogleSheetId(dto.getGoogleSheetId());
            } else {
                entity.setGoogleSheetId(googleSheetsService.extractSheetId(dto.getGoogleSheetUrl()));
            }
        }
        if (dto.getSubmoduleColumn() != null) {
            entity.setSubmoduleColumn(dto.getSubmoduleColumn());
        }
        if (dto.getSubmoduleStartRow() != null) {
            entity.setSubmoduleStartRow(dto.getSubmoduleStartRow());
        }
        if (dto.getGoogleUserEmail() != null) {
            entity.setGoogleUserEmail(dto.getGoogleUserEmail());
        }
        if (dto.getCaseIdColumn() != null) {
            entity.setCaseIdColumn(dto.getCaseIdColumn());
        }
        if (dto.getTestCaseDescriptionColumn() != null) {
            entity.setTestCaseDescriptionColumn(dto.getTestCaseDescriptionColumn());
        }
        if (dto.getTestCaseExpectedResultColumn() != null) {
            entity.setTestCaseExpectedResultColumn(dto.getTestCaseExpectedResultColumn());
        }
        if (dto.getExcludedTabs() != null) {
            entity.setExcludedTabs(dto.getExcludedTabs());
        }

        return entity;
    }

    /**
     * Filter out excluded tabs from the list of sheet names
     *
     * @param sheetNames The list of all sheet names
     * @param excludedTabs Comma-separated list of tab names to exclude
     * @return Filtered list of sheet names
     */
    private List<String> filterExcludedTabs(List<String> sheetNames, String excludedTabs) {
        if (excludedTabs == null || excludedTabs.trim().isEmpty()) {
            return sheetNames;
        }

        // Parse excluded tabs (case-insensitive, trimmed)
        List<String> excludedTabsList = Arrays.stream(excludedTabs.split(","))
                .map(String::trim)
                .filter(tab -> !tab.isEmpty())
                .map(String::toLowerCase)
                .collect(Collectors.toList());

        if (excludedTabsList.isEmpty()) {
            return sheetNames;
        }

        // Filter out excluded tabs
        return sheetNames.stream()
                .filter(sheetName -> !excludedTabsList.contains(sheetName.toLowerCase()))
                .collect(Collectors.toList());
    }

}
