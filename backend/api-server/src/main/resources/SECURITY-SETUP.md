# Security Setup Guide for OAuth Token Encryption

This guide explains how to set up secure encryption for OAuth tokens in the Enosis Portal application.

## Overview

The application now encrypts OAuth tokens using AES-256-GCM encryption before storing them in the database. This follows industry security standards and protects sensitive user credentials.

## Encryption Key Generation

### Step 1: Generate Encryption Key

Run this Java code to generate a secure encryption key:

```java
import com.enosisbd.api.server.service.security.TokenEncryptionService;

public class KeyGenerator {
    public static void main(String[] args) {
        String encryptionKey = TokenEncryptionService.generateEncryptionKey();
        System.out.println("Generated encryption key: " + encryptionKey);
    }
}
```

Or use this command line approach:

```bash
# Using OpenSSL (Linux/Mac)
openssl rand -base64 32

# Using Java keytool
keytool -genseckey -alias oauth-encryption -keyalg AES -keysize 256 -storetype JCEKS -keystore oauth-keystore.jceks
```

### Step 2: Set Environment Variable

Set the generated key as an environment variable:

```bash
# Development
export app.security.token.encryption.key=your_generated_base64_key_here

# Production (Docker)
docker run -e app.security.token.encryption.key=your_generated_base64_key_here your-app

# Kubernetes
apiVersion: v1
kind: Secret
metadata:
  name: oauth-encryption-key
data:
  encryption-key: your_base64_encoded_key_here
```

## Security Levels Comparison

### Level 1: No Encryption (NOT RECOMMENDED)
```java
// Direct storage - vulnerable to database breaches
user.setGoogleAccessToken(plainToken);
```

### Level 2: Application-Level Encryption (CURRENT IMPLEMENTATION)
```java
// AES-256-GCM encryption - industry standard
String encrypted = tokenEncryptionService.encryptToken(plainToken);
user.setGoogleAccessToken(encrypted);
```

### Level 3: Database-Level Encryption (ENTERPRISE)
```sql
-- PostgreSQL with TDE (Transparent Data Encryption)
CREATE TABLE users (
    google_access_token_encrypted BYTEA ENCRYPTED
);
```

### Level 4: External Key Management (HIGH SECURITY)
```java
// Using AWS KMS, Azure Key Vault, HashiCorp Vault
@Service
public class KMSTokenService {
    public String encryptToken(String token) {
        return awsKMS.encrypt(token, keyId);
    }
}
```

## Production Deployment Checklist

### ✅ Required Security Measures

1. **Encryption Key Management**
   - [ ] Generate unique encryption key for each environment
   - [ ] Store key in secure environment variables (not in code)
   - [ ] Use different keys for dev/staging/production
   - [ ] Implement key rotation strategy

2. **Database Security**
   - [ ] Enable database encryption at rest
   - [ ] Use SSL/TLS for database connections
   - [ ] Implement database access controls
   - [ ] Regular security audits

3. **Application Security**
   - [ ] Enable HTTPS only
   - [ ] Implement proper session management
   - [ ] Use secure headers (HSTS, CSP, etc.)
   - [ ] Regular dependency updates

4. **Monitoring & Logging**
   - [ ] Monitor token encryption/decryption failures
   - [ ] Log security events (without sensitive data)
   - [ ] Set up alerts for suspicious activities
   - [ ] Regular security assessments

### 🔒 Key Rotation Strategy

```java
@Component
public class KeyRotationService {
    
    @Scheduled(cron = "0 0 2 1 * ?") // Monthly at 2 AM
    public void rotateEncryptionKey() {
        // 1. Generate new key
        // 2. Re-encrypt all tokens with new key
        // 3. Update key in secure storage
        // 4. Verify all tokens can be decrypted
    }
}
```

## Alternative Approaches Comparison

### 1. Database Storage with Encryption (RECOMMENDED)
**Pros:**
- Industry standard approach
- Supports offline access
- Better performance
- Automatic token refresh

**Cons:**
- Requires secure key management
- Database security dependency

### 2. External Token Storage (Redis/Vault)
**Pros:**
- Centralized token management
- Built-in encryption
- Easy to scale

**Cons:**
- Additional infrastructure
- Network dependency
- Complexity

### 3. No Token Storage (Re-authenticate each time)
**Pros:**
- No storage security concerns
- Always fresh tokens

**Cons:**
- Poor user experience
- Rate limiting issues
- Performance impact

### 4. Client-Side Storage Only
**Pros:**
- No server-side storage

**Cons:**
- Security vulnerability
- Not suitable for server-to-server calls
- Token exposure risk

## Compliance & Standards

This implementation follows:

- **OWASP Cryptographic Storage Cheat Sheet**
- **OAuth 2.0 Security Best Practices (RFC 6819)**
- **NIST Cybersecurity Framework**
- **SOC 2 Type II requirements**
- **GDPR data protection requirements**

## Troubleshooting

### Common Issues

1. **Encryption Key Not Set**
   ```
   Error: Token encryption key not configured
   Solution: Set app.security.token.encryption.key environment variable
   ```

2. **Invalid Key Format**
   ```
   Error: Invalid encryption key format
   Solution: Ensure key is valid Base64 encoded AES-256 key
   ```

3. **Decryption Failures**
   ```
   Error: Token decryption failed
   Solution: Check if key has changed or tokens corrupted
   ```

### Validation Commands

```bash
# Test encryption key validity
curl -X GET http://localhost:8085/api/admin/security/validate-encryption

# Check token encryption status
curl -X GET http://localhost:8085/api/admin/security/token-status
```

## Migration from Unencrypted Tokens

If you have existing unencrypted tokens in the database:

```java
@Component
public class TokenMigrationService {
    
    public void migrateExistingTokens() {
        // 1. Find all users with unencrypted tokens
        // 2. Encrypt existing tokens
        // 3. Update database
        // 4. Verify migration success
    }
}
```

This ensures a secure, compliant, and industry-standard approach to OAuth token management.
