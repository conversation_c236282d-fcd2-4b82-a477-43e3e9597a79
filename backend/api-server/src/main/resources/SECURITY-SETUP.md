# Security Setup Guide for OAuth Token Encryption

This guide explains how to set up secure encryption for OAuth tokens in the Enosis Portal application.

## Overview

The application now encrypts OAuth tokens using AES-256-GCM encryption before storing them in the database. This follows industry security standards and protects sensitive user credentials.

## Encryption Key Generation

### Step 1: Generate Encryption Key

Run this Java code to generate a secure encryption key:

```java
import com.enosisbd.api.server.service.security.TokenEncryptionService;

public class KeyGenerator {
    public static void main(String[] args) {
        String encryptionKey = TokenEncryptionService.generateEncryptionKey();
        System.out.println("Generated encryption key: " + encryptionKey);
    }
}
```

Or use this command line approach:

```bash
# Using OpenSSL (Linux/Mac)
openssl rand -base64 32

# Using Java keytool
keytool -genseckey -alias oauth-encryption -keyalg AES -keysize 256 -storetype JCEKS -keystore oauth-keystore.jceks
```

### Step 2: Set Environment Variable

Set the generated key as an environment variable:

```bash
# Development
export app.security.token.encryption.key=vIp3qK8LYIbR7WiH+z6s7kZmvKo29+nmTQ5uDYBqrE8=

# Production (Docker)
docker run -e app.security.token.encryption.key=vIp3qK8LYIbR7WiH+z6s7kZmvKo29+nmTQ5uDYBqrE8=

# Kubernetes
apiVersion: v1
kind: Secret
metadata:
  name: oauth-encryption-key
data:
  encryption-key: vIp3qK8LYIbR7WiH+z6s7kZmvKo29+nmTQ5uDYBqrE8=
```


### Validation Commands

```bash
# Test encryption key validity
curl -X GET http://localhost:8085/api/admin/security/validate-encryption

# Check token encryption status
curl -X GET http://localhost:8085/api/admin/security/token-status
```
